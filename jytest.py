#!/usr/bin/env python3
"""
Simple test runner that executes Jest tests using available Node.js tools
"""

import subprocess
import sys
import os

def run_command(cmd):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return 1, "", str(e)

def main():
    """Main test runner function"""
    print("🧪 Running TypeScript Jest tests...")
    
    # Check if we're in the right directory
    if not os.path.exists("src/temperature.test.ts"):
        print("❌ Test files not found. Make sure you're in the project root.")
        sys.exit(1)
    
    # Try different ways to run Jest
    commands_to_try = [
        "npx jest",
        "node_modules/.bin/jest",
        "jest"
    ]
    
    for cmd in commands_to_try:
        print(f"Trying: {cmd}")
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode == 0:
            print("✅ Tests completed successfully!")
            print(stdout)
            return
        elif "command not found" not in stderr.lower() and "not found" not in stderr.lower():
            # Jest was found but tests failed
            print("❌ Tests failed:")
            print(stdout)
            print(stderr)
            sys.exit(returncode)
    
    print("❌ Jest not found. Please install Node.js and Jest dependencies first.")
    print("Try running: npm install")
    sys.exit(1)

if __name__ == "__main__":
    main()
