#!/bin/bash

# Main run script for the temperature conversion project

set -e  # Exit on any error

# Check if argument is provided
if [ "$1" = "test" ]; then
    echo "🧪 Running tests..."
    if command -v uv &> /dev/null; then
        uv run jest
    elif command -v npm &> /dev/null; then
        npm test
    else
        echo "❌ Neither uv nor npm found. Please install one of them."
        exit 1
    fi
elif [ "$1" = "build" ]; then
    echo "🔨 Building project..."
    if command -v uv &> /dev/null; then
        uv run tsc
    elif command -v npm &> /dev/null; then
        npm run build
    else
        echo "❌ Neither uv nor npm found. Please install one of them."
        exit 1
    fi
else
    echo "🚀 Running main program..."
    if command -v uv &> /dev/null; then
        uv run src/main.ts
    elif command -v npm &> /dev/null; then
        npm run dev
    else
        echo "❌ Neither uv nor npm found. Please install one of them."
        exit 1
    fi
fi
