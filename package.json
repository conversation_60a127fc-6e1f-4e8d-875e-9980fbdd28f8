{"name": "far2cel", "version": "1.0.0", "description": "Temperature conversion utilities between Fahrenheit and Celsius", "main": "dist/main.js", "scripts": {"build": "tsc", "test": "jest", "test:watch": "jest --watch", "start": "node dist/main.js", "dev": "ts-node src/main.ts"}, "keywords": ["temperature", "conversion", "fahrenheit", "celsius"], "author": "", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.6.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}