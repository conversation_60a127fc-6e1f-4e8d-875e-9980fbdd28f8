import {
  fahrenheitToCelsius,
  celsiusToFahrenheit,
  isFreezingPoint,
  isBoilingPoint,
  TEMPERATURE_CONSTANTS,
} from './temperature';

/**
 * Main program to demonstrate temperature conversion functionality
 */
function main(): void {
  console.log('🌡️  Temperature Conversion Utility\n');
  
  // Demonstrate basic conversions
  console.log('=== Basic Conversions ===');
  const fahrenheitTemps = [32, 68, 98.6, 212, -40];
  
  fahrenheitTemps.forEach(temp => {
    const celsius = fahrenheitToCelsius(temp);
    console.log(`${temp}°F = ${celsius}°C`);
  });
  
  console.log('\n=== Reverse Conversions ===');
  const celsiusTemps = [0, 20, 37, 100, -40];
  
  celsiusTemps.forEach(temp => {
    const fahrenheit = celsiusToFahrenheit(temp);
    console.log(`${temp}°C = ${fahrenheit}°F`);
  });
  
  // Demonstrate special temperature points
  console.log('\n=== Special Temperature Points ===');
  console.log(`Absolute Zero: ${TEMPERATURE_CONSTANTS.ABSOLUTE_ZERO_CELSIUS}°C = ${TEMPERATURE_CONSTANTS.ABSOLUTE_ZERO_FAHRENHEIT}°F`);
  console.log(`Water Freezing: ${TEMPERATURE_CONSTANTS.WATER_FREEZING_CELSIUS}°C = ${TEMPERATURE_CONSTANTS.WATER_FREEZING_FAHRENHEIT}°F`);
  console.log(`Water Boiling: ${TEMPERATURE_CONSTANTS.WATER_BOILING_CELSIUS}°C = ${TEMPERATURE_CONSTANTS.WATER_BOILING_FAHRENHEIT}°F`);
  
  // Demonstrate utility functions
  console.log('\n=== Temperature Checks ===');
  console.log(`Is 0°C freezing point? ${isFreezingPoint(0)}`);
  console.log(`Is 1°C freezing point? ${isFreezingPoint(1)}`);
  console.log(`Is 100°C boiling point? ${isBoilingPoint(100)}`);
  console.log(`Is 99°C boiling point? ${isBoilingPoint(99)}`);
  
  // Demonstrate error handling
  console.log('\n=== Error Handling Examples ===');
  try {
    fahrenheitToCelsius(-500);
  } catch (error) {
    console.log(`Error: ${(error as Error).message}`);
  }
  
  try {
    celsiusToFahrenheit(-300);
  } catch (error) {
    console.log(`Error: ${(error as Error).message}`);
  }
  
  console.log('\n✅ Temperature conversion demo completed!');
}

// Run the main function if this file is executed directly
if (require.main === module) {
  main();
}
