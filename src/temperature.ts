/**
 * Temperature conversion utilities between Fahrenheit and Celsius
 */

/**
 * Converts temperature from Fahrenheit to Celsius
 * Formula: C = (F - 32) × 5/9
 * 
 * @param fahrenheit - Temperature in Fahrenheit
 * @returns Temperature in Celsius
 * @throws Error if input is not a valid number or below absolute zero
 */
export function fahrenheitToCelsius(fahrenheit: number): number {
  if (typeof fahrenheit !== 'number' || isNaN(fahrenheit)) {
    throw new Error('Input must be a valid number');
  }
  
  // Absolute zero in Fahrenheit is -459.67°F
  if (fahrenheit < -459.67) {
    throw new Error('Temperature cannot be below absolute zero (-459.67°F)');
  }
  
  const celsius = (fahrenheit - 32) * (5 / 9);
  
  // Round to 2 decimal places to avoid floating point precision issues
  return Math.round(celsius * 100) / 100;
}

/**
 * Converts temperature from Celsius to Fahrenheit
 * Formula: F = C × 9/5 + 32
 * 
 * @param celsius - Temperature in Celsius
 * @returns Temperature in Fahrenheit
 * @throws Error if input is not a valid number or below absolute zero
 */
export function celsiusToFahrenheit(celsius: number): number {
  if (typeof celsius !== 'number' || isNaN(celsius)) {
    throw new Error('Input must be a valid number');
  }
  
  // Absolute zero in Celsius is -273.15°C
  if (celsius < -273.15) {
    throw new Error('Temperature cannot be below absolute zero (-273.15°C)');
  }
  
  const fahrenheit = celsius * (9 / 5) + 32;
  
  // Round to 2 decimal places to avoid floating point precision issues
  return Math.round(fahrenheit * 100) / 100;
}

/**
 * Checks if a temperature is at the freezing point of water
 * 
 * @param celsius - Temperature in Celsius
 * @returns True if temperature is at freezing point (0°C)
 */
export function isFreezingPoint(celsius: number): boolean {
  return celsius === 0;
}

/**
 * Checks if a temperature is at the boiling point of water at sea level
 * 
 * @param celsius - Temperature in Celsius
 * @returns True if temperature is at boiling point (100°C)
 */
export function isBoilingPoint(celsius: number): boolean {
  return celsius === 100;
}

/**
 * Temperature conversion constants
 */
export const TEMPERATURE_CONSTANTS = {
  ABSOLUTE_ZERO_CELSIUS: -273.15,
  ABSOLUTE_ZERO_FAHRENHEIT: -459.67,
  WATER_FREEZING_CELSIUS: 0,
  WATER_FREEZING_FAHRENHEIT: 32,
  WATER_BOILING_CELSIUS: 100,
  WATER_BOILING_FAHRENHEIT: 212,
} as const;
