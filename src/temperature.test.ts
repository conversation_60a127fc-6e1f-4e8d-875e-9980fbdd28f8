import {
  fahrenheitToCelsius,
  celsiusToFahrenheit,
  isFreezingPoint,
  isBoilingPoint,
  TEMPERATURE_CONSTANTS,
} from './temperature';

describe('Temperature Conversion Functions', () => {
  describe('fahrenheitToCelsius', () => {
    test('converts freezing point correctly', () => {
      expect(fahrenheitToCelsius(32)).toBe(0);
    });

    test('converts boiling point correctly', () => {
      expect(fahrenheitToCelsius(212)).toBe(100);
    });

    test('converts absolute zero correctly', () => {
      expect(fahrenheitToCelsius(-459.67)).toBe(-273.15);
    });

    test('converts room temperature correctly', () => {
      expect(fahrenheitToCelsius(68)).toBe(20);
    });

    test('converts negative temperatures correctly', () => {
      expect(fahrenheitToCelsius(-40)).toBe(-40);
    });

    test('handles decimal inputs correctly', () => {
      expect(fahrenheitToCelsius(98.6)).toBe(37);
    });

    test('rounds to 2 decimal places', () => {
      expect(fahrenheitToCelsius(100)).toBe(37.78);
    });

    test('throws error for non-number input', () => {
      expect(() => fahrenheitToCelsius(NaN)).toThrow('Input must be a valid number');
      expect(() => fahrenheitToCelsius('32' as any)).toThrow('Input must be a valid number');
    });

    test('throws error for temperature below absolute zero', () => {
      expect(() => fahrenheitToCelsius(-500)).toThrow('Temperature cannot be below absolute zero (-459.67°F)');
    });
  });

  describe('celsiusToFahrenheit', () => {
    test('converts freezing point correctly', () => {
      expect(celsiusToFahrenheit(0)).toBe(32);
    });

    test('converts boiling point correctly', () => {
      expect(celsiusToFahrenheit(100)).toBe(212);
    });

    test('converts absolute zero correctly', () => {
      expect(celsiusToFahrenheit(-273.15)).toBe(-459.67);
    });

    test('converts room temperature correctly', () => {
      expect(celsiusToFahrenheit(20)).toBe(68);
    });

    test('converts negative temperatures correctly', () => {
      expect(celsiusToFahrenheit(-40)).toBe(-40);
    });

    test('handles decimal inputs correctly', () => {
      expect(celsiusToFahrenheit(37)).toBe(98.6);
    });

    test('rounds to 2 decimal places', () => {
      expect(celsiusToFahrenheit(25)).toBe(77);
    });

    test('throws error for non-number input', () => {
      expect(() => celsiusToFahrenheit(NaN)).toThrow('Input must be a valid number');
      expect(() => celsiusToFahrenheit('0' as any)).toThrow('Input must be a valid number');
    });

    test('throws error for temperature below absolute zero', () => {
      expect(() => celsiusToFahrenheit(-300)).toThrow('Temperature cannot be below absolute zero (-273.15°C)');
    });
  });

  describe('Bidirectional conversion accuracy', () => {
    test('fahrenheit to celsius and back should return original value', () => {
      const originalFahrenheit = 75.5;
      const celsius = fahrenheitToCelsius(originalFahrenheit);
      const backToFahrenheit = celsiusToFahrenheit(celsius);
      expect(backToFahrenheit).toBeCloseTo(originalFahrenheit, 2);
    });

    test('celsius to fahrenheit and back should return original value', () => {
      const originalCelsius = 23.7;
      const fahrenheit = celsiusToFahrenheit(originalCelsius);
      const backToCelsius = fahrenheitToCelsius(fahrenheit);
      expect(backToCelsius).toBeCloseTo(originalCelsius, 2);
    });
  });

  describe('isFreezingPoint', () => {
    test('returns true for 0°C', () => {
      expect(isFreezingPoint(0)).toBe(true);
    });

    test('returns false for non-zero temperatures', () => {
      expect(isFreezingPoint(1)).toBe(false);
      expect(isFreezingPoint(-1)).toBe(false);
      expect(isFreezingPoint(0.1)).toBe(false);
    });
  });

  describe('isBoilingPoint', () => {
    test('returns true for 100°C', () => {
      expect(isBoilingPoint(100)).toBe(true);
    });

    test('returns false for non-boiling temperatures', () => {
      expect(isBoilingPoint(99)).toBe(false);
      expect(isBoilingPoint(101)).toBe(false);
      expect(isBoilingPoint(99.9)).toBe(false);
    });
  });

  describe('TEMPERATURE_CONSTANTS', () => {
    test('has correct constant values', () => {
      expect(TEMPERATURE_CONSTANTS.ABSOLUTE_ZERO_CELSIUS).toBe(-273.15);
      expect(TEMPERATURE_CONSTANTS.ABSOLUTE_ZERO_FAHRENHEIT).toBe(-459.67);
      expect(TEMPERATURE_CONSTANTS.WATER_FREEZING_CELSIUS).toBe(0);
      expect(TEMPERATURE_CONSTANTS.WATER_FREEZING_FAHRENHEIT).toBe(32);
      expect(TEMPERATURE_CONSTANTS.WATER_BOILING_CELSIUS).toBe(100);
      expect(TEMPERATURE_CONSTANTS.WATER_BOILING_FAHRENHEIT).toBe(212);
    });
  });
});
